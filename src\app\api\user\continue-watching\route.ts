import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongodb';

export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const userId = url.searchParams.get('userId');

    // If no userId is provided, return empty data
    if (!userId) {
      return NextResponse.json({
        success: true,
        items: [],
        message: 'No user ID provided'
      });
    }

    // Connect to MongoDB
    await ensureMongooseConnection();

    // Try to get user's watch history
    try {
      // Import the UserContentInteraction model
      const { default: UserContentInteraction } = await import('@/models/UserContentInteraction');
      
      // Get the user's recent watch history with progress > 5% and < 95%
      const watchHistory = await UserContentInteraction.find({
        userId,
        interactionType: 'view',
        progress: { $gte: 5, $lte: 95 }
      })
      .sort({ timestamp: -1 })
      .limit(10)
      .populate('contentId')
      .lean();

      // Transform the data to match the expected format
      const continueWatchingItems = watchHistory
        .filter(item => item.contentId) // Only items with valid content
        .map(item => ({
          id: item.contentId._id.toString(),
          title: item.contentId.title,
          image: item.contentId.posterPath || '/placeholder-poster.jpg',
          type: item.contentId.type as 'show' | 'movie',
          progress: item.progress || 0,
          episode: item.contentId.type === 'show' ? `Episode ${item.episode || 1}` : undefined,
          season: item.contentId.type === 'show' ? item.season || 1 : undefined,
          currentTime: item.currentTime || 0,
          duration: item.duration || 0,
          timestamp: item.timestamp
        }));

      return NextResponse.json({
        success: true,
        items: continueWatchingItems,
        message: 'Continue watching data retrieved successfully'
      });

    } catch (dbError) {
      console.error('Database error getting watch history:', dbError);
      
      // If there's a database error, return empty data
      return NextResponse.json({
        success: true,
        items: [],
        message: 'No watch history found'
      });
    }

  } catch (error) {
    console.error('Error in continue watching API:', error);
    
    // Return empty data in case of error
    return NextResponse.json({
      success: true,
      items: [],
      message: 'Error retrieving continue watching data'
    });
  }
}
