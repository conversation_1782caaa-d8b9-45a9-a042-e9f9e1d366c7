'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Users, Play, Plus, Search, Clock, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Navbar } from '@/components/Navbar';
import { toast } from 'sonner';

interface WatchParty {
  id: string;
  title: string;
  hostName: string;
  hostAvatar?: string;
  memberCount: number;
  maxMembers: number;
  isPrivate: boolean;
  createdAt: string;
  contentId: string;
  contentType: 'movie' | 'show';
  currentTime?: number;
  isPlaying?: boolean;
}

export default function WatchPartyPage() {
  const router = useRouter();
  const [parties, setParties] = useState<WatchParty[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('public');

  useEffect(() => {
    // Simulate loading watch parties
    const loadParties = async () => {
      try {
        setLoading(true);
        
        // TODO: Implement real API call when watch party backend is ready
        // const response = await fetch('/api/watch-party/active');
        // const data = await response.json();
        // setParties(data.parties || []);
        
        // For now, return empty array since feature is not yet implemented
        setParties([]);
      } catch (error) {
        console.error('Error loading watch parties:', error);
        toast.error('Failed to load watch parties');
      } finally {
        // Mock data - in real implementation, fetch from API
        const mockParties: WatchParty[] = [
          {
            id: '1',
            title: 'Avengers: Endgame',
            hostName: 'John Doe',
            hostAvatar: '/avatars/avatar-1.png',
            memberCount: 3,
            maxMembers: 10,
            isPrivate: false,
            createdAt: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
            contentId: 'tt4154796',
            contentType: 'movie',
            currentTime: 1200, // 20 minutes
            isPlaying: true
          },
          {
            id: '2',
            title: 'Breaking Bad S01E01',
            hostName: 'Jane Smith',
            hostAvatar: '/avatars/avatar-2.png',
            memberCount: 5,
            maxMembers: 8,
            isPrivate: false,
            createdAt: new Date(Date.now() - 1000 * 60 * 15).toISOString(), // 15 minutes ago
            contentId: 'tt0903747',
            contentType: 'show',
            currentTime: 600, // 10 minutes
            isPlaying: false
          }
        ];

        setParties(mockParties);
      } catch (error) {
        console.error('Error loading watch parties:', error);
        toast.error('Failed to load watch parties');
      } finally {
        setLoading(false);
      }
    };

    loadParties();
  }, []);

  const handleCreateParty = () => {
    router.push('/watch-party/create');
  };

  const handleJoinParty = (partyId: string) => {
    router.push(`/watch-party/${partyId}`);
  };

  const filteredParties = parties.filter(party => 
    party.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    party.hostName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}`;
    }
    return `${minutes}:00`;
  };

  return (
    <div className="min-h-screen bg-vista-dark">
      <Navbar />
      
      <div className="pt-20 pb-8">
        <div className="container mx-auto px-4">
          {/* Header */}
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
            <div className="mb-6 lg:mb-0">
              <h1 className="text-4xl font-bold text-vista-light mb-2">
                Watch Parties
              </h1>
              <p className="text-vista-light/70">
                Watch movies and shows together with friends in real-time
              </p>
            </div>
            
            <Button
              onClick={handleCreateParty}
              className="bg-vista-blue hover:bg-vista-blue/90 text-white"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Watch Party
            </Button>
          </div>

          {/* Search and Tabs */}
          <div className="mb-8">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-vista-light/50" />
                <Input
                  placeholder="Search parties or hosts..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-vista-dark-lighter border-vista-light/20 text-vista-light placeholder:text-vista-light/50"
                />
              </div>
              
              <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full lg:w-auto">
                <TabsList className="bg-vista-dark-lighter border-vista-light/20">
                  <TabsTrigger value="public" className="text-vista-light data-[state=active]:bg-vista-blue">
                    Public Parties
                  </TabsTrigger>
                  <TabsTrigger value="private" className="text-vista-light data-[state=active]:bg-vista-blue">
                    Private Parties
                  </TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
          </div>

          {/* Content */}
          <TabsContent value="public" className="mt-0">
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-vista-light">Loading watch parties...</div>
              </div>
            ) : filteredParties.length === 0 ? (
              <div className="text-center py-12">
                <Users className="h-16 w-16 text-vista-light/30 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-vista-light mb-2">
                  No Watch Parties Found
                </h3>
                <p className="text-vista-light/70 mb-6">
                  {searchTerm ? 'Try adjusting your search terms.' : 'Be the first to create a watch party!'}
                </p>
                <Button onClick={handleCreateParty} className="bg-vista-blue hover:bg-vista-blue/90">
                  <Plus className="h-4 w-4 mr-2" />
                  Create First Party
                </Button>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredParties.map((party) => (
                  <Card key={party.id} className="bg-vista-dark-lighter border-vista-light/10 hover:border-vista-blue/30 transition-colors">
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center space-x-3">
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={party.hostAvatar} />
                            <AvatarFallback className="bg-vista-blue text-white">
                              {party.hostName.charAt(0)}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium text-vista-light">{party.hostName}</p>
                            <p className="text-sm text-vista-light/50">{formatTimeAgo(party.createdAt)}</p>
                          </div>
                        </div>
                        <Badge variant="outline" className="border-vista-blue/30 text-vista-blue">
                          {party.contentType}
                        </Badge>
                      </div>
                    </CardHeader>
                    
                    <CardContent className="space-y-4">
                      <div>
                        <h3 className="font-semibold text-vista-light mb-2 line-clamp-2">
                          {party.title}
                        </h3>
                        
                        <div className="flex items-center justify-between text-sm text-vista-light/70">
                          <div className="flex items-center space-x-4">
                            <div className="flex items-center space-x-1">
                              <Users className="h-3 w-3" />
                              <span>{party.memberCount}/{party.maxMembers}</span>
                            </div>
                            {party.currentTime && (
                              <div className="flex items-center space-x-1">
                                <Clock className="h-3 w-3" />
                                <span>{formatTime(party.currentTime)}</span>
                              </div>
                            )}
                          </div>
                          
                          {party.isPlaying && (
                            <div className="flex items-center space-x-1 text-green-500">
                              <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
                              <span className="text-xs">Live</span>
                            </div>
                          )}
                        </div>
                      </div>
                      
                      <Button
                        onClick={() => handleJoinParty(party.id)}
                        className="w-full bg-vista-blue hover:bg-vista-blue/90 text-white"
                      >
                        <Play className="h-4 w-4 mr-2" />
                        Join Party
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="private" className="mt-0">
            <div className="text-center py-12">
              <div className="h-16 w-16 bg-vista-dark-lighter rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="h-8 w-8 text-vista-light/30" />
              </div>
              <h3 className="text-xl font-semibold text-vista-light mb-2">
                Private Watch Parties
              </h3>
              <p className="text-vista-light/70 mb-6">
                Private parties are invite-only. Ask your friends to share their party links with you.
              </p>
            </div>
          </TabsContent>
        </div>
      </div>
    </div>
  );
} 